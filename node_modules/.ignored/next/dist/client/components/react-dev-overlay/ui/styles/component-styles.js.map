{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/styles/component-styles.tsx"], "sourcesContent": ["import { CODE_FRAME_STYLES } from '../components/code-frame/code-frame'\nimport { styles as dialog } from '../components/dialog'\nimport { styles as errorLayout } from '../components/errors/error-overlay-layout/error-overlay-layout'\nimport { styles as bottomStack } from '../components/errors/error-overlay-bottom-stack'\nimport { styles as pagination } from '../components/errors/error-overlay-pagination/error-overlay-pagination'\nimport { styles as overlay } from '../components/overlay/styles'\nimport { styles as footer } from '../components/errors/error-overlay-footer/error-overlay-footer'\nimport { TERMINAL_STYLES } from '../components/terminal/terminal'\nimport { styles as toast } from '../components/toast'\nimport { styles as versionStaleness } from '../components/version-staleness-info/version-staleness-info'\nimport { styles as buildErrorStyles } from '../container/build-error'\nimport { styles as containerErrorStyles } from '../container/errors'\nimport { styles as containerRuntimeErrorStyles } from '../container/runtime-error'\nimport { COPY_BUTTON_STYLES } from '../components/copy-button'\nimport { CALL_STACK_FRAME_STYLES } from '../components/call-stack-frame/call-stack-frame'\nimport { DEV_TOOLS_INDICATOR_STYLES } from '../components/errors/dev-tools-indicator/dev-tools-indicator'\nimport { css } from '../../utils/css'\nimport { EDITOR_LINK_STYLES } from '../components/terminal/editor-link'\nimport { ENVIRONMENT_NAME_LABEL_STYLES } from '../components/errors/environment-name-label/environment-name-label'\nimport { DEV_TOOLS_INFO_STYLES } from '../components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info'\nimport { DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES } from '../components/errors/dev-tools-indicator/dev-tools-info/turbopack-info'\nimport { DEV_TOOLS_INFO_ROUTE_INFO_STYLES } from '../components/errors/dev-tools-indicator/dev-tools-info/route-info'\nimport { DEV_TOOLS_INFO_USER_PREFERENCES_STYLES } from '../components/errors/dev-tools-indicator/dev-tools-info/user-preferences'\nimport { FADER_STYLES } from '../components/fader'\n\nexport function ComponentStyles() {\n  return (\n    <style>\n      {css`\n        ${COPY_BUTTON_STYLES}\n        ${CALL_STACK_FRAME_STYLES}\n        ${ENVIRONMENT_NAME_LABEL_STYLES}\n        ${overlay}\n        ${toast}\n        ${dialog}\n        ${errorLayout}\n        ${footer}\n        ${bottomStack}\n        ${pagination}\n        ${CODE_FRAME_STYLES}\n        ${TERMINAL_STYLES}\n        ${EDITOR_LINK_STYLES}\n        ${buildErrorStyles}\n        ${containerErrorStyles}\n        ${containerRuntimeErrorStyles}\n        ${versionStaleness}\n        ${DEV_TOOLS_INDICATOR_STYLES}\n        ${DEV_TOOLS_INFO_STYLES}\n        ${DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES}\n        ${DEV_TOOLS_INFO_ROUTE_INFO_STYLES}\n        ${DEV_TOOLS_INFO_USER_PREFERENCES_STYLES}\n        ${FADER_STYLES}\n      `}\n    </style>\n  )\n}\n"], "names": ["ComponentStyles", "style", "css", "COPY_BUTTON_STYLES", "CALL_STACK_FRAME_STYLES", "ENVIRONMENT_NAME_LABEL_STYLES", "overlay", "toast", "dialog", "errorLayout", "footer", "bottomStack", "pagination", "CODE_FRAME_STYLES", "TERMINAL_STYLES", "EDITOR_LINK_STYLES", "buildErrorStyles", "containerErrorStyles", "containerRuntimeErrorStyles", "versionStaleness", "DEV_TOOLS_INDICATOR_STYLES", "DEV_TOOLS_INFO_STYLES", "DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES", "DEV_TOOLS_INFO_ROUTE_INFO_STYLES", "DEV_TOOLS_INFO_USER_PREFERENCES_STYLES", "FADER_STYLES"], "mappings": ";;;;+BAyBgBA;;;eAAAA;;;;;2BAzBkB;wBACD;oCACK;yCACA;wCACD;wBACH;oCACD;0BACD;uBACA;sCACW;4BACA;wBACI;8BACO;4BACnB;gCACK;mCACG;qBACvB;4BACe;sCACW;8BACR;+BACe;2BACJ;iCACM;uBAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtB,SAASA;IACd,qBACE,qBAACC;sBACEC,QAAG,qBACAC,8BAAkB,EAClBC,uCAAuB,EACvBC,mDAA6B,EAC7BC,cAAO,EACPC,aAAK,EACLC,cAAM,EACNC,0BAAW,EACXC,0BAAM,EACNC,+BAAW,EACXC,8BAAU,EACVC,4BAAiB,EACjBC,yBAAe,EACfC,8BAAkB,EAClBC,kBAAgB,EAChBC,cAAoB,EACpBC,oBAA2B,EAC3BC,4BAAgB,EAChBC,6CAA0B,EAC1BC,mCAAqB,EACrBC,mDAAoC,EACpCC,2CAAgC,EAChCC,uDAAsC,EACtCC,mBAAY;;AAItB"}