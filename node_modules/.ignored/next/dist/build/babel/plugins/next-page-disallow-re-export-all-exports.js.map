{"version": 3, "sources": ["../../../../src/build/babel/plugins/next-page-disallow-re-export-all-exports.ts"], "sourcesContent": ["import type { NodePath, types } from 'next/dist/compiled/babel/core'\nimport type { PluginObj } from 'next/dist/compiled/babel/core'\n\nexport default function NextPageDisallowReExportAllExports(): PluginObj<any> {\n  return {\n    visitor: {\n      ExportAllDeclaration(path: NodePath<types.ExportAllDeclaration>) {\n        const err = new SyntaxError(\n          `Using \\`export * from '...'\\` in a page is disallowed. Please use \\`export { default } from '...'\\` instead.\\n` +\n            `Read more: https://nextjs.org/docs/messages/export-all-in-page`\n        )\n        ;(err as any).code = 'BABEL_PARSE_ERROR'\n        ;(err as any).loc =\n          path.node.loc?.start ?? path.node.loc?.end ?? path.node.loc\n        throw err\n      },\n    },\n  }\n}\n"], "names": ["NextPageDisallowReExportAllExports", "visitor", "ExportAllDeclaration", "path", "err", "SyntaxError", "code", "loc", "node", "start", "end"], "mappings": ";;;;+BAGA;;;eAAwBA;;;AAAT,SAASA;IACtB,OAAO;QACLC,SAAS;YACPC,sBAAqBC,IAA0C;oBAO3DA,gBAAwBA;gBAN1B,MAAMC,MAAM,qBAGX,CAHW,IAAIC,YACd,CAAC,8GAA8G,CAAC,GAC9G,CAAC,8DAA8D,CAAC,GAFxD,qBAAA;2BAAA;gCAAA;kCAAA;gBAGZ;gBACED,IAAYE,IAAI,GAAG;gBACnBF,IAAYG,GAAG,GACfJ,EAAAA,iBAAAA,KAAKK,IAAI,CAACD,GAAG,qBAAbJ,eAAeM,KAAK,OAAIN,kBAAAA,KAAKK,IAAI,CAACD,GAAG,qBAAbJ,gBAAeO,GAAG,KAAIP,KAAKK,IAAI,CAACD,GAAG;gBAC7D,MAAMH;YACR;QACF;IACF;AACF"}