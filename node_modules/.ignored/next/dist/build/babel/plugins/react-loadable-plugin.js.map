{"version": 3, "sources": ["../../../../src/build/babel/plugins/react-loadable-plugin.ts"], "sourcesContent": ["/**\nCOPYRIGHT (c) 2017-present <PERSON> <<EMAIL>>\n MIT License\n Permission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n The above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWAR\n*/\n// This file is https://github.com/jamiebuilds/react-loadable/blob/master/src/babel.js\n// Modified to also look for `next/dynamic`\n// Modified to put `webpack` and `modules` under `loadableGenerated` to be backwards compatible with next/dynamic which has a `modules` key\n// Modified to support `dynamic(import('something'))` and `dynamic(import('something'), options)\n\nimport type {\n  NodePath,\n  types as BabelTypes,\n} from 'next/dist/compiled/babel/core'\nimport type { PluginObj } from 'next/dist/compiled/babel/core'\n\nimport { relative as relativePath } from 'path'\n\nexport default function ({\n  types: t,\n}: {\n  types: typeof BabelTypes\n}): PluginObj {\n  return {\n    visitor: {\n      ImportDeclaration(\n        path: NodePath<BabelTypes.ImportDeclaration>,\n        state: any\n      ) {\n        let source = path.node.source.value\n        if (source !== 'next/dynamic') return\n\n        let defaultSpecifier = path.get('specifiers').find((specifier) => {\n          return specifier.isImportDefaultSpecifier()\n        })\n\n        if (!defaultSpecifier) return\n\n        const bindingName = defaultSpecifier.node.local.name\n        const binding = path.scope.getBinding(bindingName)\n\n        if (!binding) {\n          return\n        }\n\n        binding.referencePaths.forEach((refPath) => {\n          let callExpression = refPath.parentPath\n\n          if (\n            callExpression.isMemberExpression() &&\n            callExpression.node.computed === false\n          ) {\n            const property = callExpression.get('property')\n            if (\n              !Array.isArray(property) &&\n              property.isIdentifier({ name: 'Map' })\n            ) {\n              callExpression = callExpression.parentPath\n            }\n          }\n\n          if (!callExpression.isCallExpression()) return\n\n          const callExpression_ =\n            callExpression as NodePath<BabelTypes.CallExpression>\n\n          let args = callExpression_.get('arguments')\n          if (args.length > 2) {\n            throw callExpression_.buildCodeFrameError(\n              'next/dynamic only accepts 2 arguments'\n            )\n          }\n\n          if (!args[0]) {\n            return\n          }\n\n          let loader\n          let options\n\n          if (args[0].isObjectExpression()) {\n            options = args[0]\n          } else {\n            if (!args[1]) {\n              callExpression_.node.arguments.push(t.objectExpression([]))\n            }\n            // This is needed as the code is modified above\n            args = callExpression_.get('arguments')\n            loader = args[0]\n            options = args[1]\n          }\n\n          if (!options.isObjectExpression()) return\n          const options_ = options as NodePath<BabelTypes.ObjectExpression>\n\n          let properties = options_.get('properties')\n          let propertiesMap: {\n            [key: string]: NodePath<\n              | BabelTypes.ObjectProperty\n              | BabelTypes.ObjectMethod\n              | BabelTypes.SpreadElement\n              | BabelTypes.BooleanLiteral\n            >\n          } = {}\n\n          properties.forEach((property) => {\n            const key: any = property.get('key')\n            propertiesMap[key.node.name] = property\n          })\n\n          if (propertiesMap.loadableGenerated) {\n            return\n          }\n\n          if (propertiesMap.loader) {\n            loader = propertiesMap.loader.get('value')\n          }\n\n          if (propertiesMap.modules) {\n            loader = propertiesMap.modules.get('value')\n          }\n\n          if (!loader || Array.isArray(loader)) {\n            return\n          }\n          const dynamicImports: BabelTypes.Expression[] = []\n          const dynamicKeys: BabelTypes.Expression[] = []\n\n          if (propertiesMap.ssr) {\n            const ssr = propertiesMap.ssr.get('value')\n            const nodePath = Array.isArray(ssr) ? undefined : ssr\n\n            if (nodePath) {\n              const nonSSR =\n                nodePath.node.type === 'BooleanLiteral' &&\n                nodePath.node.value === false\n              // If `ssr` is set to `false`, erase the loader for server side\n              if (nonSSR && loader && state.file.opts.caller?.isServer) {\n                loader.replaceWith(\n                  t.arrowFunctionExpression([], t.nullLiteral(), true)\n                )\n              }\n            }\n          }\n\n          loader.traverse({\n            Import(importPath) {\n              const importArguments = importPath.parentPath.get('arguments')\n              if (!Array.isArray(importArguments)) return\n              const node: any = importArguments[0].node\n              dynamicImports.push(node)\n              dynamicKeys.push(\n                t.binaryExpression(\n                  '+',\n                  t.stringLiteral(\n                    (state.file.opts.caller?.srcDir\n                      ? relativePath(\n                          state.file.opts.caller.srcDir,\n                          state.file.opts.filename\n                        )\n                      : state.file.opts.filename) + ' -> '\n                  ),\n                  node\n                )\n              )\n            },\n          })\n\n          if (!dynamicImports.length) return\n\n          options.node.properties.push(\n            t.objectProperty(\n              t.identifier('loadableGenerated'),\n              t.objectExpression(\n                state.file.opts.caller?.isDev ||\n                  state.file.opts.caller?.isServer\n                  ? [\n                      t.objectProperty(\n                        t.identifier('modules'),\n                        t.arrayExpression(dynamicKeys)\n                      ),\n                    ]\n                  : [\n                      t.objectProperty(\n                        t.identifier('webpack'),\n                        t.arrowFunctionExpression(\n                          [],\n                          t.arrayExpression(\n                            dynamicImports.map((dynamicImport) => {\n                              return t.callExpression(\n                                t.memberExpression(\n                                  t.identifier('require'),\n                                  t.identifier('resolveWeak')\n                                ),\n                                [dynamicImport]\n                              )\n                            })\n                          )\n                        )\n                      ),\n                    ]\n              )\n            )\n          )\n\n          // Turns `dynamic(import('something'))` into `dynamic(() => import('something'))` for backwards compat.\n          // This is the replicate the behavior in versions below Next.js 7 where we magically handled not executing the `import()` too.\n          // We'll deprecate this behavior and provide a codemod for it in 7.1.\n          if (loader.isCallExpression()) {\n            const arrowFunction = t.arrowFunctionExpression([], loader.node)\n            loader.replaceWith(arrowFunction)\n          }\n        })\n      },\n    },\n  }\n}\n"], "names": ["types", "t", "visitor", "ImportDeclaration", "path", "state", "source", "node", "value", "defaultSpecifier", "get", "find", "specifier", "isImportDefaultSpecifier", "bindingName", "local", "name", "binding", "scope", "getBinding", "referencePaths", "for<PERSON>ach", "refPath", "callExpression", "parentPath", "isMemberExpression", "computed", "property", "Array", "isArray", "isIdentifier", "isCallExpression", "callExpression_", "args", "length", "buildCodeFrameError", "loader", "options", "isObjectExpression", "arguments", "push", "objectExpression", "options_", "properties", "propertiesMap", "key", "loadableGenerated", "modules", "dynamicImports", "dynamic<PERSON>eys", "ssr", "nodePath", "undefined", "nonSSR", "type", "file", "opts", "caller", "isServer", "replaceWith", "arrowFunctionExpression", "nullLiteral", "traverse", "Import", "importPath", "importArguments", "binaryExpression", "stringLiteral", "srcDir", "relativePath", "filename", "objectProperty", "identifier", "isDev", "arrayExpression", "map", "dynamicImport", "memberExpression", "arrowFunction"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;AAmBA,GACA,sFAAsF;AACtF,2CAA2C;AAC3C,2IAA2I;AAC3I,gGAAgG;;;;;+BAUhG;;;eAAA;;;sBAFyC;AAE1B,SAAf,SAAyB,EACvBA,OAAOC,CAAC,EAGT;IACC,OAAO;QACLC,SAAS;YACPC,mBACEC,IAA4C,EAC5CC,KAAU;gBAEV,IAAIC,SAASF,KAAKG,IAAI,CAACD,MAAM,CAACE,KAAK;gBACnC,IAAIF,WAAW,gBAAgB;gBAE/B,IAAIG,mBAAmBL,KAAKM,GAAG,CAAC,cAAcC,IAAI,CAAC,CAACC;oBAClD,OAAOA,UAAUC,wBAAwB;gBAC3C;gBAEA,IAAI,CAACJ,kBAAkB;gBAEvB,MAAMK,cAAcL,iBAAiBF,IAAI,CAACQ,KAAK,CAACC,IAAI;gBACpD,MAAMC,UAAUb,KAAKc,KAAK,CAACC,UAAU,CAACL;gBAEtC,IAAI,CAACG,SAAS;oBACZ;gBACF;gBAEAA,QAAQG,cAAc,CAACC,OAAO,CAAC,CAACC;wBAiIxBjB,yBACEA;oBAjIR,IAAIkB,iBAAiBD,QAAQE,UAAU;oBAEvC,IACED,eAAeE,kBAAkB,MACjCF,eAAehB,IAAI,CAACmB,QAAQ,KAAK,OACjC;wBACA,MAAMC,WAAWJ,eAAeb,GAAG,CAAC;wBACpC,IACE,CAACkB,MAAMC,OAAO,CAACF,aACfA,SAASG,YAAY,CAAC;4BAAEd,MAAM;wBAAM,IACpC;4BACAO,iBAAiBA,eAAeC,UAAU;wBAC5C;oBACF;oBAEA,IAAI,CAACD,eAAeQ,gBAAgB,IAAI;oBAExC,MAAMC,kBACJT;oBAEF,IAAIU,OAAOD,gBAAgBtB,GAAG,CAAC;oBAC/B,IAAIuB,KAAKC,MAAM,GAAG,GAAG;wBACnB,MAAMF,gBAAgBG,mBAAmB,CACvC;oBAEJ;oBAEA,IAAI,CAACF,IAAI,CAAC,EAAE,EAAE;wBACZ;oBACF;oBAEA,IAAIG;oBACJ,IAAIC;oBAEJ,IAAIJ,IAAI,CAAC,EAAE,CAACK,kBAAkB,IAAI;wBAChCD,UAAUJ,IAAI,CAAC,EAAE;oBACnB,OAAO;wBACL,IAAI,CAACA,IAAI,CAAC,EAAE,EAAE;4BACZD,gBAAgBzB,IAAI,CAACgC,SAAS,CAACC,IAAI,CAACvC,EAAEwC,gBAAgB,CAAC,EAAE;wBAC3D;wBACA,+CAA+C;wBAC/CR,OAAOD,gBAAgBtB,GAAG,CAAC;wBAC3B0B,SAASH,IAAI,CAAC,EAAE;wBAChBI,UAAUJ,IAAI,CAAC,EAAE;oBACnB;oBAEA,IAAI,CAACI,QAAQC,kBAAkB,IAAI;oBACnC,MAAMI,WAAWL;oBAEjB,IAAIM,aAAaD,SAAShC,GAAG,CAAC;oBAC9B,IAAIkC,gBAOA,CAAC;oBAELD,WAAWtB,OAAO,CAAC,CAACM;wBAClB,MAAMkB,MAAWlB,SAASjB,GAAG,CAAC;wBAC9BkC,aAAa,CAACC,IAAItC,IAAI,CAACS,IAAI,CAAC,GAAGW;oBACjC;oBAEA,IAAIiB,cAAcE,iBAAiB,EAAE;wBACnC;oBACF;oBAEA,IAAIF,cAAcR,MAAM,EAAE;wBACxBA,SAASQ,cAAcR,MAAM,CAAC1B,GAAG,CAAC;oBACpC;oBAEA,IAAIkC,cAAcG,OAAO,EAAE;wBACzBX,SAASQ,cAAcG,OAAO,CAACrC,GAAG,CAAC;oBACrC;oBAEA,IAAI,CAAC0B,UAAUR,MAAMC,OAAO,CAACO,SAAS;wBACpC;oBACF;oBACA,MAAMY,iBAA0C,EAAE;oBAClD,MAAMC,cAAuC,EAAE;oBAE/C,IAAIL,cAAcM,GAAG,EAAE;wBACrB,MAAMA,MAAMN,cAAcM,GAAG,CAACxC,GAAG,CAAC;wBAClC,MAAMyC,WAAWvB,MAAMC,OAAO,CAACqB,OAAOE,YAAYF;wBAElD,IAAIC,UAAU;gCAKY9C;4BAJxB,MAAMgD,SACJF,SAAS5C,IAAI,CAAC+C,IAAI,KAAK,oBACvBH,SAAS5C,IAAI,CAACC,KAAK,KAAK;4BAC1B,+DAA+D;4BAC/D,IAAI6C,UAAUjB,YAAU/B,2BAAAA,MAAMkD,IAAI,CAACC,IAAI,CAACC,MAAM,qBAAtBpD,yBAAwBqD,QAAQ,GAAE;gCACxDtB,OAAOuB,WAAW,CAChB1D,EAAE2D,uBAAuB,CAAC,EAAE,EAAE3D,EAAE4D,WAAW,IAAI;4BAEnD;wBACF;oBACF;oBAEAzB,OAAO0B,QAAQ,CAAC;wBACdC,QAAOC,UAAU;gCASR3D;4BARP,MAAM4D,kBAAkBD,WAAWxC,UAAU,CAACd,GAAG,CAAC;4BAClD,IAAI,CAACkB,MAAMC,OAAO,CAACoC,kBAAkB;4BACrC,MAAM1D,OAAY0D,eAAe,CAAC,EAAE,CAAC1D,IAAI;4BACzCyC,eAAeR,IAAI,CAACjC;4BACpB0C,YAAYT,IAAI,CACdvC,EAAEiE,gBAAgB,CAChB,KACAjE,EAAEkE,aAAa,CACb,AAAC9D,CAAAA,EAAAA,0BAAAA,MAAMkD,IAAI,CAACC,IAAI,CAACC,MAAM,qBAAtBpD,wBAAwB+D,MAAM,IAC3BC,IAAAA,cAAY,EACVhE,MAAMkD,IAAI,CAACC,IAAI,CAACC,MAAM,CAACW,MAAM,EAC7B/D,MAAMkD,IAAI,CAACC,IAAI,CAACc,QAAQ,IAE1BjE,MAAMkD,IAAI,CAACC,IAAI,CAACc,QAAQ,AAAD,IAAK,SAElC/D;wBAGN;oBACF;oBAEA,IAAI,CAACyC,eAAed,MAAM,EAAE;oBAE5BG,QAAQ9B,IAAI,CAACoC,UAAU,CAACH,IAAI,CAC1BvC,EAAEsE,cAAc,CACdtE,EAAEuE,UAAU,CAAC,sBACbvE,EAAEwC,gBAAgB,CAChBpC,EAAAA,0BAAAA,MAAMkD,IAAI,CAACC,IAAI,CAACC,MAAM,qBAAtBpD,wBAAwBoE,KAAK,OAC3BpE,2BAAAA,MAAMkD,IAAI,CAACC,IAAI,CAACC,MAAM,qBAAtBpD,yBAAwBqD,QAAQ,IAC9B;wBACEzD,EAAEsE,cAAc,CACdtE,EAAEuE,UAAU,CAAC,YACbvE,EAAEyE,eAAe,CAACzB;qBAErB,GACD;wBACEhD,EAAEsE,cAAc,CACdtE,EAAEuE,UAAU,CAAC,YACbvE,EAAE2D,uBAAuB,CACvB,EAAE,EACF3D,EAAEyE,eAAe,CACf1B,eAAe2B,GAAG,CAAC,CAACC;4BAClB,OAAO3E,EAAEsB,cAAc,CACrBtB,EAAE4E,gBAAgB,CAChB5E,EAAEuE,UAAU,CAAC,YACbvE,EAAEuE,UAAU,CAAC,iBAEf;gCAACI;6BAAc;wBAEnB;qBAIP;oBAKX,uGAAuG;oBACvG,8HAA8H;oBAC9H,qEAAqE;oBACrE,IAAIxC,OAAOL,gBAAgB,IAAI;wBAC7B,MAAM+C,gBAAgB7E,EAAE2D,uBAAuB,CAAC,EAAE,EAAExB,OAAO7B,IAAI;wBAC/D6B,OAAOuB,WAAW,CAACmB;oBACrB;gBACF;YACF;QACF;IACF;AACF"}