{"version": 3, "sources": ["../../../../src/build/segment-config/app/app-segment-config.ts"], "sourcesContent": ["import { z } from 'next/dist/compiled/zod'\nimport { formatZodError } from '../../../shared/lib/zod'\n\n/**\n * The schema for configuration for a page.\n */\nconst AppSegmentConfigSchema = z.object({\n  /**\n   * The number of seconds to revalidate the page or false to disable revalidation.\n   */\n  revalidate: z\n    .union([z.number().int().nonnegative(), z.literal(false)])\n    .optional(),\n\n  /**\n   * Whether the page supports dynamic parameters.\n   */\n  dynamicParams: z.boolean().optional(),\n\n  /**\n   * The dynamic behavior of the page.\n   */\n  dynamic: z\n    .enum(['auto', 'error', 'force-static', 'force-dynamic'])\n    .optional(),\n\n  /**\n   * The caching behavior of the page.\n   */\n  fetchCache: z\n    .enum([\n      'auto',\n      'default-cache',\n      'only-cache',\n      'force-cache',\n      'force-no-store',\n      'default-no-store',\n      'only-no-store',\n    ])\n    .optional(),\n\n  /**\n   * The preferred region for the page.\n   */\n  preferredRegion: z.union([z.string(), z.array(z.string())]).optional(),\n\n  /**\n   * Whether the page supports partial prerendering. When true, the page will be\n   * served using partial prerendering. This setting will only take affect if\n   * it's enabled via the `experimental.ppr = \"incremental\"` option.\n   */\n  experimental_ppr: z.boolean().optional(),\n\n  /**\n   * The runtime to use for the page.\n   */\n  runtime: z.enum(['edge', 'nodejs']).optional(),\n\n  /**\n   * The maximum duration for the page in seconds.\n   */\n  maxDuration: z.number().int().nonnegative().optional(),\n})\n\n/**\n * Parse the app segment config.\n * @param data - The data to parse.\n * @param route - The route of the app.\n * @returns The parsed app segment config.\n */\nexport function parseAppSegmentConfig(\n  data: unknown,\n  route: string\n): AppSegmentConfig {\n  const parsed = AppSegmentConfigSchema.safeParse(data, {\n    errorMap: (issue, ctx) => {\n      if (issue.path.length === 1 && issue.path[0] === 'revalidate') {\n        return {\n          message: `Invalid revalidate value ${JSON.stringify(\n            ctx.data\n          )} on \"${route}\", must be a non-negative number or false`,\n        }\n      }\n\n      return { message: ctx.defaultError }\n    },\n  })\n\n  if (!parsed.success) {\n    throw formatZodError(\n      `Invalid segment configuration options detected for \"${route}\". Read more at https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config`,\n      parsed.error\n    )\n  }\n\n  return parsed.data\n}\n\n/**\n * The configuration for a page.\n */\nexport type AppSegmentConfig = {\n  /**\n   * The revalidation period for the page in seconds, or false to disable ISR.\n   */\n  revalidate?: number | false\n\n  /**\n   * Whether the page supports dynamic parameters.\n   */\n  dynamicParams?: boolean\n\n  /**\n   * The dynamic behavior of the page.\n   */\n  dynamic?: 'auto' | 'error' | 'force-static' | 'force-dynamic'\n\n  /**\n   * The caching behavior of the page.\n   */\n  fetchCache?:\n    | 'auto'\n    | 'default-cache'\n    | 'default-no-store'\n    | 'force-cache'\n    | 'force-no-store'\n    | 'only-cache'\n    | 'only-no-store'\n\n  /**\n   * The preferred region for the page.\n   */\n  preferredRegion?: string | string[]\n\n  /**\n   * Whether the page supports partial prerendering. When true, the page will be\n   * served using partial prerendering. This setting will only take affect if\n   * it's enabled via the `experimental.ppr = \"incremental\"` option.\n   */\n  experimental_ppr?: boolean\n\n  /**\n   * The runtime to use for the page.\n   */\n  runtime?: 'edge' | 'nodejs'\n\n  /**\n   * The maximum duration for the page in seconds.\n   */\n  maxDuration?: number\n}\n\n/**\n * The keys of the configuration for a page.\n *\n * @internal - required to exclude zod types from the build\n */\nexport const AppSegmentConfigSchemaKeys = AppSegmentConfigSchema.keyof().options\n"], "names": ["AppSegmentConfigSchemaKeys", "parseAppSegmentConfig", "AppSegmentConfigSchema", "z", "object", "revalidate", "union", "number", "int", "nonnegative", "literal", "optional", "dynamicParams", "boolean", "dynamic", "enum", "fetchCache", "preferredRegion", "string", "array", "experimental_ppr", "runtime", "maxDuration", "data", "route", "parsed", "safeParse", "errorMap", "issue", "ctx", "path", "length", "message", "JSON", "stringify", "defaultError", "success", "formatZodError", "error", "keyof", "options"], "mappings": ";;;;;;;;;;;;;;;IA6JaA,0BAA0B;eAA1BA;;IAvFGC,qBAAqB;eAArBA;;;qBAtEE;sBACa;AAE/B;;CAEC,GACD,MAAMC,yBAAyBC,MAAC,CAACC,MAAM,CAAC;IACtC;;GAEC,GACDC,YAAYF,MAAC,CACVG,KAAK,CAAC;QAACH,MAAC,CAACI,MAAM,GAAGC,GAAG,GAAGC,WAAW;QAAIN,MAAC,CAACO,OAAO,CAAC;KAAO,EACxDC,QAAQ;IAEX;;GAEC,GACDC,eAAeT,MAAC,CAACU,OAAO,GAAGF,QAAQ;IAEnC;;GAEC,GACDG,SAASX,MAAC,CACPY,IAAI,CAAC;QAAC;QAAQ;QAAS;QAAgB;KAAgB,EACvDJ,QAAQ;IAEX;;GAEC,GACDK,YAAYb,MAAC,CACVY,IAAI,CAAC;QACJ;QACA;QACA;QACA;QACA;QACA;QACA;KACD,EACAJ,QAAQ;IAEX;;GAEC,GACDM,iBAAiBd,MAAC,CAACG,KAAK,CAAC;QAACH,MAAC,CAACe,MAAM;QAAIf,MAAC,CAACgB,KAAK,CAAChB,MAAC,CAACe,MAAM;KAAI,EAAEP,QAAQ;IAEpE;;;;GAIC,GACDS,kBAAkBjB,MAAC,CAACU,OAAO,GAAGF,QAAQ;IAEtC;;GAEC,GACDU,SAASlB,MAAC,CAACY,IAAI,CAAC;QAAC;QAAQ;KAAS,EAAEJ,QAAQ;IAE5C;;GAEC,GACDW,aAAanB,MAAC,CAACI,MAAM,GAAGC,GAAG,GAAGC,WAAW,GAAGE,QAAQ;AACtD;AAQO,SAASV,sBACdsB,IAAa,EACbC,KAAa;IAEb,MAAMC,SAASvB,uBAAuBwB,SAAS,CAACH,MAAM;QACpDI,UAAU,CAACC,OAAOC;YAChB,IAAID,MAAME,IAAI,CAACC,MAAM,KAAK,KAAKH,MAAME,IAAI,CAAC,EAAE,KAAK,cAAc;gBAC7D,OAAO;oBACLE,SAAS,CAAC,yBAAyB,EAAEC,KAAKC,SAAS,CACjDL,IAAIN,IAAI,EACR,KAAK,EAAEC,MAAM,yCAAyC,CAAC;gBAC3D;YACF;YAEA,OAAO;gBAAEQ,SAASH,IAAIM,YAAY;YAAC;QACrC;IACF;IAEA,IAAI,CAACV,OAAOW,OAAO,EAAE;QACnB,MAAMC,IAAAA,oBAAc,EAClB,CAAC,oDAAoD,EAAEb,MAAM,+FAA+F,CAAC,EAC7JC,OAAOa,KAAK;IAEhB;IAEA,OAAOb,OAAOF,IAAI;AACpB;AA6DO,MAAMvB,6BAA6BE,uBAAuBqC,KAAK,GAAGC,OAAO"}