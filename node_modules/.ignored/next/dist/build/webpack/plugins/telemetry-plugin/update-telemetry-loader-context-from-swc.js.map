{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/telemetry-plugin/update-telemetry-loader-context-from-swc.ts"], "sourcesContent": ["import type { TelemetryLoaderContext } from './telemetry-plugin'\nexport type SwcTransformTelemetryOutput = {\n  eliminatedPackages?: string\n  useCacheTelemetryTracker?: string\n}\n\nexport function updateTelemetryLoaderCtxFromTransformOutput(\n  ctx: TelemetryLoaderContext,\n  output: SwcTransformTelemetryOutput\n) {\n  if (output.eliminatedPackages && ctx.eliminatedPackages) {\n    for (const pkg of JSON.parse(output.eliminatedPackages)) {\n      ctx.eliminatedPackages.add(pkg)\n    }\n  }\n\n  if (output.useCacheTelemetryTracker && ctx.useCacheTracker) {\n    for (const [key, value] of JSON.parse(output.useCacheTelemetryTracker)) {\n      const prefixedKey = `useCache/${key}` as const\n      const numericValue = Number(value)\n      if (!isNaN(numericValue)) {\n        ctx.useCacheTracker.set(\n          prefixedKey,\n          (ctx.useCacheTracker.get(prefixedKey) || 0) + numericValue\n        )\n      }\n    }\n  }\n}\n"], "names": ["updateTelemetryLoaderCtxFromTransformOutput", "ctx", "output", "eliminatedPackages", "pkg", "JSON", "parse", "add", "useCacheTelemetryTracker", "useCacheTracker", "key", "value", "prefixedKey", "numericValue", "Number", "isNaN", "set", "get"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;AAAT,SAASA,4CACdC,GAA2B,EAC3BC,MAAmC;IAEnC,IAAIA,OAAOC,kBAAkB,IAAIF,IAAIE,kBAAkB,EAAE;QACvD,KAAK,MAAMC,OAAOC,KAAKC,KAAK,CAACJ,OAAOC,kBAAkB,EAAG;YACvDF,IAAIE,kBAAkB,CAACI,GAAG,CAACH;QAC7B;IACF;IAEA,IAAIF,OAAOM,wBAAwB,IAAIP,IAAIQ,eAAe,EAAE;QAC1D,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIN,KAAKC,KAAK,CAACJ,OAAOM,wBAAwB,EAAG;YACtE,MAAMI,cAAc,CAAC,SAAS,EAAEF,KAAK;YACrC,MAAMG,eAAeC,OAAOH;YAC5B,IAAI,CAACI,MAAMF,eAAe;gBACxBZ,IAAIQ,eAAe,CAACO,GAAG,CACrBJ,aACA,AAACX,CAAAA,IAAIQ,eAAe,CAACQ,GAAG,CAACL,gBAAgB,CAAA,IAAKC;YAElD;QACF;IACF;AACF"}