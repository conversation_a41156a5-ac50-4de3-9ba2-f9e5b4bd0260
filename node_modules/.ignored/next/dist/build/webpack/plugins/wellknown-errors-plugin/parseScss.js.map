{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseScss.ts"], "sourcesContent": ["import { bold, cyan, red, yellow } from '../../../../lib/picocolors'\nimport { SimpleWebpackError } from './simpleWebpackError'\n\nconst regexScssError =\n  /SassError: (.+)\\n\\s+on line (\\d+) [\\s\\S]*?>> (.+)\\n\\s*(-+)\\^$/m\n\nexport function getScssError(\n  fileName: string,\n  fileContent: string | null,\n  err: Error\n): SimpleWebpackError | false {\n  if (err.name !== 'SassError') {\n    return false\n  }\n\n  const res = regexScssError.exec(err.message)\n  if (res) {\n    const [, reason, _lineNumer, backupFrame, columnString] = res\n    const lineNumber = Math.max(1, parseInt(_lineNumer, 10))\n    const column = columnString?.length ?? 1\n\n    let frame: string | undefined\n    if (fileContent) {\n      try {\n        const {\n          codeFrameColumns,\n        } = require('next/dist/compiled/babel/code-frame')\n        frame = codeFrameColumns(\n          fileContent,\n          { start: { line: lineNumber, column } },\n          { forceColor: true }\n        ) as string\n      } catch {}\n    }\n\n    return new SimpleWebpackError(\n      `${cyan(fileName)}:${yellow(lineNumber.toString())}:${yellow(\n        column.toString()\n      )}`,\n      red(bold('Syntax error')).concat(`: ${reason}\\n\\n${frame ?? backupFrame}`)\n    )\n  }\n\n  return false\n}\n"], "names": ["getScssError", "regexScssError", "fileName", "fileContent", "err", "name", "res", "exec", "message", "reason", "_lineNumer", "<PERSON><PERSON><PERSON><PERSON>", "columnString", "lineNumber", "Math", "max", "parseInt", "column", "length", "frame", "codeFrameColumns", "require", "start", "line", "forceColor", "SimpleWebpackError", "cyan", "yellow", "toString", "red", "bold", "concat"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;4BANwB;oCACL;AAEnC,MAAMC,iBACJ;AAEK,SAASD,aACdE,QAAgB,EAChBC,WAA0B,EAC1BC,GAAU;IAEV,IAAIA,IAAIC,IAAI,KAAK,aAAa;QAC5B,OAAO;IACT;IAEA,MAAMC,MAAML,eAAeM,IAAI,CAACH,IAAII,OAAO;IAC3C,IAAIF,KAAK;QACP,MAAM,GAAGG,QAAQC,YAAYC,aAAaC,aAAa,GAAGN;QAC1D,MAAMO,aAAaC,KAAKC,GAAG,CAAC,GAAGC,SAASN,YAAY;QACpD,MAAMO,SAASL,CAAAA,gCAAAA,aAAcM,MAAM,KAAI;QAEvC,IAAIC;QACJ,IAAIhB,aAAa;YACf,IAAI;gBACF,MAAM,EACJiB,gBAAgB,EACjB,GAAGC,QAAQ;gBACZF,QAAQC,iBACNjB,aACA;oBAAEmB,OAAO;wBAAEC,MAAMV;wBAAYI;oBAAO;gBAAE,GACtC;oBAAEO,YAAY;gBAAK;YAEvB,EAAE,OAAM,CAAC;QACX;QAEA,OAAO,IAAIC,sCAAkB,CAC3B,GAAGC,IAAAA,gBAAI,EAACxB,UAAU,CAAC,EAAEyB,IAAAA,kBAAM,EAACd,WAAWe,QAAQ,IAAI,CAAC,EAAED,IAAAA,kBAAM,EAC1DV,OAAOW,QAAQ,KACd,EACHC,IAAAA,eAAG,EAACC,IAAAA,gBAAI,EAAC,iBAAiBC,MAAM,CAAC,CAAC,EAAE,EAAEtB,OAAO,IAAI,EAAEU,SAASR,aAAa;IAE7E;IAEA,OAAO;AACT"}