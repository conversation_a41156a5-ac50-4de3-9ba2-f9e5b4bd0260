{"version": 3, "sources": ["../../../../../../../src/build/webpack/config/blocks/css/loaders/client.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { getRspackCore } from '../../../../../../shared/lib/get-rspack'\n\nexport function getClientStyleLoader({\n  hasAppDir,\n  isAppDir,\n  isDevelopment,\n  assetPrefix,\n}: {\n  hasAppDir: boolean\n  isAppDir?: boolean\n  isDevelopment: boolean\n  assetPrefix: string\n}): webpack.RuleSetUseItem {\n  const isRspack = Boolean(process.env.NEXT_RSPACK)\n  const shouldEnableApp = typeof isAppDir === 'boolean' ? isAppDir : hasAppDir\n\n  // Keep next-style-loader for development mode in `pages/`\n  if (isDevelopment && !shouldEnableApp) {\n    return {\n      loader: 'next-style-loader',\n      options: {\n        insert: function (element: Node) {\n          // By default, style-loader injects CSS into the bottom\n          // of <head>. This causes ordering problems between dev\n          // and prod. To fix this, we render a <noscript> tag as\n          // an anchor for the styles to be placed before. These\n          // styles will be applied _before_ <style jsx global>.\n\n          // These elements should always exist. If they do not,\n          // this code should fail.\n          var anchorElement = document.querySelector(\n            '#__next_css__DO_NOT_USE__'\n          )!\n          var parentNode = anchorElement.parentNode! // Normally <head>\n\n          // Each style tag should be placed right before our\n          // anchor. By inserting before and not after, we do not\n          // need to track the last inserted element.\n          parentNode.insertBefore(element, anchorElement)\n        },\n      },\n    }\n  }\n\n  const MiniCssExtractPlugin = isRspack\n    ? getRspackCore().rspack.CssExtractRspackPlugin\n    : require('../../../../plugins/mini-css-extract-plugin').default\n\n  return {\n    loader: MiniCssExtractPlugin.loader,\n    options: {\n      publicPath: `${assetPrefix}/_next/`,\n      esModule: false,\n    },\n  }\n}\n"], "names": ["getClientStyleLoader", "hasAppDir", "isAppDir", "isDevelopment", "assetPrefix", "isRspack", "Boolean", "process", "env", "NEXT_RSPACK", "shouldEnableApp", "loader", "options", "insert", "element", "anchorElement", "document", "querySelector", "parentNode", "insertBefore", "MiniCssExtractPlugin", "getRspackCore", "rspack", "CssExtractRspackPlugin", "require", "default", "publicPath", "esModule"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;2BAFc;AAEvB,SAASA,qBAAqB,EACnCC,SAAS,EACTC,QAAQ,EACRC,aAAa,EACbC,WAAW,EAMZ;IACC,MAAMC,WAAWC,QAAQC,QAAQC,GAAG,CAACC,WAAW;IAChD,MAAMC,kBAAkB,OAAOR,aAAa,YAAYA,WAAWD;IAEnE,0DAA0D;IAC1D,IAAIE,iBAAiB,CAACO,iBAAiB;QACrC,OAAO;YACLC,QAAQ;YACRC,SAAS;gBACPC,QAAQ,SAAUC,OAAa;oBAC7B,uDAAuD;oBACvD,uDAAuD;oBACvD,uDAAuD;oBACvD,sDAAsD;oBACtD,sDAAsD;oBAEtD,sDAAsD;oBACtD,yBAAyB;oBACzB,IAAIC,gBAAgBC,SAASC,aAAa,CACxC;oBAEF,IAAIC,aAAaH,cAAcG,UAAU,AAAE,kBAAkB;;oBAE7D,mDAAmD;oBACnD,uDAAuD;oBACvD,2CAA2C;oBAC3CA,WAAWC,YAAY,CAACL,SAASC;gBACnC;YACF;QACF;IACF;IAEA,MAAMK,uBAAuBf,WACzBgB,IAAAA,wBAAa,IAAGC,MAAM,CAACC,sBAAsB,GAC7CC,QAAQ,+CAA+CC,OAAO;IAElE,OAAO;QACLd,QAAQS,qBAAqBT,MAAM;QACnCC,SAAS;YACPc,YAAY,GAAGtB,YAAY,OAAO,CAAC;YACnCuB,UAAU;QACZ;IACF;AACF"}