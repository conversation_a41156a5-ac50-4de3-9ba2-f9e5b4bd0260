{"version": 3, "sources": ["../../../../../../src/build/webpack/config/blocks/css/index.ts"], "sourcesContent": ["import curry from 'next/dist/compiled/lodash.curry'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { loader, plugin } from '../../helpers'\nimport { pipe } from '../../utils'\nimport type { ConfigurationContext, ConfigurationFn } from '../../utils'\nimport { getCssModuleLoader, getGlobalCssLoader } from './loaders'\nimport { getNextFontLoader } from './loaders/next-font'\nimport {\n  getCustomDocumentError,\n  getGlobalImportError,\n  getGlobalModuleImportError,\n  getLocalModuleImportError,\n} from './messages'\nimport { getPostCssPlugins } from './plugins'\nimport { nonNullable } from '../../../../../lib/non-nullable'\nimport { WEBPACK_LAYERS } from '../../../../../lib/constants'\nimport { getRspackCore } from '../../../../../shared/lib/get-rspack'\n\n// RegExps for all Style Sheet variants\nexport const regexLikeCss = /\\.(css|scss|sass)$/\n\n// RegExps for Style Sheets\nconst regexCssGlobal = /(?<!\\.module)\\.css$/\nconst regexCssModules = /\\.module\\.css$/\n\n// RegExps for Syntactically Awesome Style Sheets\nconst regexSassGlobal = /(?<!\\.module)\\.(scss|sass)$/\nconst regexSassModules = /\\.module\\.(scss|sass)$/\n\nconst APP_LAYER_RULE = {\n  or: [\n    WEBPACK_LAYERS.reactServerComponents,\n    WEBPACK_LAYERS.serverSideRendering,\n    WEBPACK_LAYERS.appPagesBrowser,\n  ],\n}\n\nconst PAGES_LAYER_RULE = {\n  not: [\n    WEBPACK_LAYERS.reactServerComponents,\n    WEBPACK_LAYERS.serverSideRendering,\n    WEBPACK_LAYERS.appPagesBrowser,\n  ],\n}\n\n/**\n * Mark a rule as removable if built-in CSS support is disabled\n */\nfunction markRemovable(r: webpack.RuleSetRule): webpack.RuleSetRule {\n  Object.defineProperty(r, Symbol.for('__next_css_remove'), {\n    enumerable: false,\n    value: true,\n  })\n  return r\n}\n\nlet postcssInstancePromise: Promise<any>\nexport async function lazyPostCSS(\n  rootDirectory: string,\n  supportedBrowsers: string[] | undefined,\n  disablePostcssPresetEnv: boolean | undefined,\n  useLightningcss: boolean | undefined\n) {\n  if (!postcssInstancePromise) {\n    postcssInstancePromise = (async () => {\n      const postcss = require('postcss')\n      // @ts-ignore backwards compat\n      postcss.plugin = function postcssPlugin(name, initializer) {\n        function creator(...args: any) {\n          let transformer = initializer(...args)\n          transformer.postcssPlugin = name\n          // transformer.postcssVersion = new Processor().version\n          return transformer\n        }\n\n        let cache: any\n        Object.defineProperty(creator, 'postcss', {\n          get() {\n            if (!cache) cache = creator()\n            return cache\n          },\n        })\n\n        creator.process = function (\n          css: any,\n          processOpts: any,\n          pluginOpts: any\n        ) {\n          return postcss([creator(pluginOpts)]).process(css, processOpts)\n        }\n\n        return creator\n      }\n\n      // @ts-ignore backwards compat\n      postcss.vendor = {\n        /**\n         * Returns the vendor prefix extracted from an input string.\n         *\n         * @example\n         * postcss.vendor.prefix('-moz-tab-size') //=> '-moz-'\n         * postcss.vendor.prefix('tab-size')      //=> ''\n         */\n        prefix: function prefix(prop: string): string {\n          const match = prop.match(/^(-\\w+-)/)\n\n          if (match) {\n            return match[0]\n          }\n\n          return ''\n        },\n\n        /**\n         * Returns the input string stripped of its vendor prefix.\n         *\n         * @example\n         * postcss.vendor.unprefixed('-moz-tab-size') //=> 'tab-size'\n         */\n        unprefixed: function unprefixed(\n          /**\n           * String with or without vendor prefix.\n           */\n          prop: string\n        ): string {\n          return prop.replace(/^-\\w+-/, '')\n        },\n      }\n\n      const postCssPlugins = await getPostCssPlugins(\n        rootDirectory,\n        supportedBrowsers,\n        disablePostcssPresetEnv,\n        useLightningcss\n      )\n\n      return {\n        postcss,\n        postcssWithPlugins: postcss(postCssPlugins),\n      }\n    })()\n  }\n\n  return postcssInstancePromise\n}\n\nexport const css = curry(async function css(\n  ctx: ConfigurationContext,\n  config: webpack.Configuration\n) {\n  const isRspack = Boolean(process.env.NEXT_RSPACK)\n  const {\n    prependData: sassPrependData,\n    additionalData: sassAdditionalData,\n    implementation: sassImplementation,\n    ...sassOptions\n  } = ctx.sassOptions\n\n  const lazyPostCSSInitializer = () =>\n    lazyPostCSS(\n      ctx.rootDirectory,\n      ctx.supportedBrowsers,\n      ctx.experimental.disablePostcssPresetEnv,\n      ctx.experimental.useLightningcss\n    )\n\n  const sassPreprocessors: webpack.RuleSetUseItem[] = [\n    // First, process files with `sass-loader`: this inlines content, and\n    // compiles away the proprietary syntax.\n    {\n      loader: require.resolve('next/dist/compiled/sass-loader'),\n      options: {\n        implementation: sassImplementation,\n        // Source maps are required so that `resolve-url-loader` can locate\n        // files original to their source directory.\n        sourceMap: true,\n        sassOptions: {\n          // The \"fibers\" option is not needed for Node.js 16+, but it's causing\n          // problems for Node.js <= 14 users as you'll have to manually install\n          // the `fibers` package:\n          // https://github.com/webpack-contrib/sass-loader#:~:text=We%20automatically%20inject%20the%20fibers%20package\n          // https://github.com/vercel/next.js/issues/45052\n          // Since it's optional and not required, we'll disable it by default\n          // to avoid the confusion.\n          fibers: false,\n          // TODO: Remove this once we upgrade to sass-loader 16\n          silenceDeprecations: ['legacy-js-api'],\n          ...sassOptions,\n        },\n        additionalData: sassPrependData || sassAdditionalData,\n      },\n    },\n    // Then, `sass-loader` will have passed-through CSS imports as-is instead\n    // of inlining them. Because they were inlined, the paths are no longer\n    // correct.\n    // To fix this, we use `resolve-url-loader` to rewrite the CSS\n    // imports to real file paths.\n    {\n      loader: require.resolve('../../../loaders/resolve-url-loader/index'),\n      options: {\n        postcss: lazyPostCSSInitializer,\n        // Source maps are not required here, but we may as well emit\n        // them.\n        sourceMap: true,\n      },\n    },\n  ]\n\n  const fns: ConfigurationFn[] = []\n\n  const googleLoader = require.resolve(\n    'next/dist/compiled/@next/font/google/loader'\n  )\n  const localLoader = require.resolve(\n    'next/dist/compiled/@next/font/local/loader'\n  )\n  const nextFontLoaders: Array<[string | RegExp, string, any?]> = [\n    [require.resolve('next/font/google/target.css'), googleLoader],\n    [require.resolve('next/font/local/target.css'), localLoader],\n  ]\n\n  nextFontLoaders.forEach(([fontLoaderTarget, fontLoaderPath]) => {\n    // Matches the resolved font loaders noop files to run next-font-loader\n    fns.push(\n      loader({\n        oneOf: [\n          markRemovable({\n            sideEffects: false,\n            test: fontLoaderTarget,\n            use: getNextFontLoader(ctx, lazyPostCSSInitializer, fontLoaderPath),\n          }),\n        ],\n      })\n    )\n  })\n\n  // CSS cannot be imported in _document. This comes before everything because\n  // global CSS nor CSS modules work in said file.\n  fns.push(\n    loader({\n      oneOf: [\n        markRemovable({\n          test: regexLikeCss,\n          // Use a loose regex so we don't have to crawl the file system to\n          // find the real file name (if present).\n          issuer: /pages[\\\\/]_document\\./,\n          use: {\n            loader: 'error-loader',\n            options: {\n              reason: getCustomDocumentError(),\n            },\n          },\n        }),\n      ],\n    })\n  )\n\n  const shouldIncludeExternalCSSImports =\n    !!ctx.experimental.craCompat || !!ctx.transpilePackages\n\n  // CSS modules & SASS modules support. They are allowed to be imported in anywhere.\n  fns.push(\n    // CSS Modules should never have side effects. This setting will\n    // allow unused CSS to be removed from the production build.\n    // We ensure this by disallowing `:global()` CSS at the top-level\n    // via the `pure` mode in `css-loader`.\n    loader({\n      oneOf: [\n        // For app dir, we need to match the specific app layer.\n        ctx.hasAppDir\n          ? markRemovable({\n              sideEffects: true,\n              test: regexCssModules,\n              issuerLayer: APP_LAYER_RULE,\n              use: [\n                {\n                  loader: require.resolve(\n                    '../../../loaders/next-flight-css-loader'\n                  ),\n                  options: {\n                    cssModules: true,\n                  },\n                },\n                ...getCssModuleLoader(\n                  { ...ctx, isAppDir: true },\n                  lazyPostCSSInitializer\n                ),\n              ],\n            })\n          : null,\n        markRemovable({\n          sideEffects: true,\n          test: regexCssModules,\n          issuerLayer: PAGES_LAYER_RULE,\n          use: getCssModuleLoader(\n            { ...ctx, isAppDir: false },\n            lazyPostCSSInitializer\n          ),\n        }),\n      ].filter(nonNullable),\n    }),\n    // Opt-in support for Sass (using .scss or .sass extensions).\n    // Sass Modules should never have side effects. This setting will\n    // allow unused Sass to be removed from the production build.\n    // We ensure this by disallowing `:global()` Sass at the top-level\n    // via the `pure` mode in `css-loader`.\n    loader({\n      oneOf: [\n        // For app dir, we need to match the specific app layer.\n        ctx.hasAppDir\n          ? markRemovable({\n              sideEffects: true,\n              test: regexSassModules,\n              issuerLayer: APP_LAYER_RULE,\n              use: [\n                {\n                  loader: require.resolve(\n                    '../../../loaders/next-flight-css-loader'\n                  ),\n                  options: {\n                    cssModules: true,\n                  },\n                },\n                ...getCssModuleLoader(\n                  { ...ctx, isAppDir: true },\n                  lazyPostCSSInitializer,\n                  sassPreprocessors\n                ),\n              ],\n            })\n          : null,\n        markRemovable({\n          sideEffects: true,\n          test: regexSassModules,\n          issuerLayer: PAGES_LAYER_RULE,\n          use: getCssModuleLoader(\n            { ...ctx, isAppDir: false },\n            lazyPostCSSInitializer,\n            sassPreprocessors\n          ),\n        }),\n      ].filter(nonNullable),\n    }),\n    // Throw an error for CSS Modules used outside their supported scope\n    loader({\n      oneOf: [\n        markRemovable({\n          test: [regexCssModules, regexSassModules],\n          use: {\n            loader: 'error-loader',\n            options: {\n              reason: getLocalModuleImportError(),\n            },\n          },\n        }),\n      ],\n    })\n  )\n\n  // Global CSS and SASS support.\n  if (ctx.isServer) {\n    fns.push(\n      loader({\n        oneOf: [\n          ctx.hasAppDir && !ctx.isProduction\n            ? markRemovable({\n                sideEffects: true,\n                test: [regexCssGlobal, regexSassGlobal],\n                issuerLayer: APP_LAYER_RULE,\n                use: {\n                  loader: require.resolve(\n                    '../../../loaders/next-flight-css-loader'\n                  ),\n                  options: {\n                    cssModules: false,\n                  },\n                },\n              })\n            : null,\n          markRemovable({\n            // CSS imports have side effects, even on the server side.\n            sideEffects: true,\n            test: [regexCssGlobal, regexSassGlobal],\n            use: require.resolve('next/dist/compiled/ignore-loader'),\n          }),\n        ].filter(nonNullable),\n      })\n    )\n  } else {\n    // External CSS files are allowed to be loaded when any of the following is true:\n    // - hasAppDir: all CSS files are allowed\n    // - If the CSS file is located in `node_modules`\n    // - If the CSS file is located in another package in a monorepo (outside of the current rootDir)\n    // - If the issuer is pages/_app (matched later)\n    const allowedPagesGlobalCSSPath = ctx.hasAppDir\n      ? undefined\n      : {\n          and: [\n            {\n              or: [\n                /node_modules/,\n                {\n                  not: [ctx.rootDirectory],\n                },\n              ],\n            },\n          ],\n        }\n    const allowedPagesGlobalCSSIssuer = ctx.hasAppDir\n      ? undefined\n      : shouldIncludeExternalCSSImports\n        ? undefined\n        : {\n            and: [ctx.rootDirectory],\n            not: [/node_modules/],\n          }\n\n    fns.push(\n      loader({\n        oneOf: [\n          ...(ctx.hasAppDir\n            ? [\n                markRemovable({\n                  sideEffects: true,\n                  test: regexCssGlobal,\n                  issuerLayer: APP_LAYER_RULE,\n                  use: [\n                    {\n                      loader: require.resolve(\n                        '../../../loaders/next-flight-css-loader'\n                      ),\n                      options: {\n                        cssModules: false,\n                      },\n                    },\n                    ...getGlobalCssLoader(\n                      { ...ctx, isAppDir: true },\n                      lazyPostCSSInitializer\n                    ),\n                  ],\n                }),\n                markRemovable({\n                  sideEffects: true,\n                  test: regexSassGlobal,\n                  issuerLayer: APP_LAYER_RULE,\n                  use: [\n                    {\n                      loader: require.resolve(\n                        '../../../loaders/next-flight-css-loader'\n                      ),\n                      options: {\n                        cssModules: false,\n                      },\n                    },\n                    ...getGlobalCssLoader(\n                      { ...ctx, isAppDir: true },\n                      lazyPostCSSInitializer,\n                      sassPreprocessors\n                    ),\n                  ],\n                }),\n              ]\n            : []),\n          markRemovable({\n            sideEffects: true,\n            test: regexCssGlobal,\n            include: allowedPagesGlobalCSSPath,\n            issuer: allowedPagesGlobalCSSIssuer,\n            issuerLayer: PAGES_LAYER_RULE,\n            use: getGlobalCssLoader(\n              { ...ctx, isAppDir: false },\n              lazyPostCSSInitializer\n            ),\n          }),\n          markRemovable({\n            sideEffects: true,\n            test: regexSassGlobal,\n            include: allowedPagesGlobalCSSPath,\n            issuer: allowedPagesGlobalCSSIssuer,\n            issuerLayer: PAGES_LAYER_RULE,\n            use: getGlobalCssLoader(\n              { ...ctx, isAppDir: false },\n              lazyPostCSSInitializer,\n              sassPreprocessors\n            ),\n          }),\n        ].filter(nonNullable),\n      })\n    )\n\n    if (ctx.customAppFile) {\n      fns.push(\n        loader({\n          oneOf: [\n            markRemovable({\n              sideEffects: true,\n              test: regexCssGlobal,\n              issuer: { and: [ctx.customAppFile] },\n              use: getGlobalCssLoader(\n                { ...ctx, isAppDir: false },\n                lazyPostCSSInitializer\n              ),\n            }),\n          ],\n        }),\n        loader({\n          oneOf: [\n            markRemovable({\n              sideEffects: true,\n              test: regexSassGlobal,\n              issuer: { and: [ctx.customAppFile] },\n              use: getGlobalCssLoader(\n                { ...ctx, isAppDir: false },\n                lazyPostCSSInitializer,\n                sassPreprocessors\n              ),\n            }),\n          ],\n        })\n      )\n    }\n  }\n\n  // Throw an error for Global CSS used inside of `node_modules`\n  if (!shouldIncludeExternalCSSImports) {\n    fns.push(\n      loader({\n        oneOf: [\n          markRemovable({\n            test: [regexCssGlobal, regexSassGlobal],\n            issuer: { and: [/node_modules/] },\n            use: {\n              loader: 'error-loader',\n              options: {\n                reason: getGlobalModuleImportError(),\n              },\n            },\n          }),\n        ],\n      })\n    )\n  }\n\n  // Throw an error for Global CSS used outside of our custom <App> file\n  fns.push(\n    loader({\n      oneOf: [\n        markRemovable({\n          test: [regexCssGlobal, regexSassGlobal],\n          issuer: ctx.hasAppDir\n            ? {\n                // If it's inside the app dir, but not importing from a layout file,\n                // throw an error.\n                and: [ctx.rootDirectory],\n                not: [/layout\\.(js|mjs|jsx|ts|tsx)$/],\n              }\n            : undefined,\n          use: {\n            loader: 'error-loader',\n            options: {\n              reason: getGlobalImportError(),\n            },\n          },\n        }),\n      ],\n    })\n  )\n\n  if (ctx.isClient) {\n    // Automatically transform references to files (i.e. url()) into URLs\n    // e.g. url(./logo.svg)\n    fns.push(\n      loader({\n        oneOf: [\n          markRemovable({\n            // This should only be applied to CSS files\n            issuer: regexLikeCss,\n            // Exclude extensions that webpack handles by default\n            exclude: [\n              /\\.(js|mjs|jsx|ts|tsx)$/,\n              /\\.html$/,\n              /\\.json$/,\n              /\\.webpack\\[[^\\]]+\\]$/,\n            ],\n            // `asset/resource` always emits a URL reference, where `asset`\n            // might inline the asset as a data URI\n            type: 'asset/resource',\n          }),\n        ],\n      })\n    )\n  }\n\n  // Enable full mini-css-extract-plugin hmr for prod mode pages or app dir\n  if (ctx.isClient && (ctx.isProduction || ctx.hasAppDir)) {\n    // Extract CSS as CSS file(s) in the client-side production bundle.\n    const MiniCssExtractPlugin = isRspack\n      ? getRspackCore().CssExtractRspackPlugin\n      : require('../../../plugins/mini-css-extract-plugin').default\n\n    fns.push(\n      plugin(\n        // @ts-ignore webpack 5 compat\n        new MiniCssExtractPlugin({\n          filename: ctx.isProduction\n            ? 'static/css/[contenthash].css'\n            : 'static/css/[name].css',\n          chunkFilename: ctx.isProduction\n            ? 'static/css/[contenthash].css'\n            : 'static/css/[name].css',\n          // Next.js guarantees that CSS order \"doesn't matter\", due to imposed\n          // restrictions:\n          // 1. Global CSS can only be defined in a single entrypoint (_app)\n          // 2. CSS Modules generate scoped class names by default and cannot\n          //    include Global CSS (:global() selector).\n          //\n          // While not a perfect guarantee (e.g. liberal use of `:global()`\n          // selector), this assumption is required to code-split CSS.\n          //\n          // If this warning were to trigger, it'd be unactionable by the user,\n          // but likely not valid -- so we disable it.\n          ignoreOrder: true,\n          insert: function (linkTag: HTMLLinkElement) {\n            if (typeof _N_E_STYLE_LOAD === 'function') {\n              const { href, onload, onerror } = linkTag\n              _N_E_STYLE_LOAD(\n                href.indexOf(window.location.origin) === 0\n                  ? new URL(href).pathname\n                  : href\n              ).then(\n                () => onload?.call(linkTag, { type: 'load' } as Event),\n                () => onerror?.call(linkTag, {} as Event)\n              )\n            } else {\n              document.head.appendChild(linkTag)\n            }\n          },\n        })\n      )\n    )\n  }\n\n  const fn = pipe(...fns)\n  return fn(config)\n})\n"], "names": ["css", "lazyPostCSS", "regexLikeCss", "regexCssGlobal", "regexCssModules", "regexSassGlobal", "regexSassModules", "APP_LAYER_RULE", "or", "WEBPACK_LAYERS", "reactServerComponents", "serverSideRendering", "appPagesBrowser", "PAGES_LAYER_RULE", "not", "markRemovable", "r", "Object", "defineProperty", "Symbol", "for", "enumerable", "value", "postcssInstancePromise", "rootDirectory", "supportedBrowsers", "disablePostcssPresetEnv", "useLightningcss", "postcss", "require", "plugin", "postcssPlugin", "name", "initializer", "creator", "args", "transformer", "cache", "get", "process", "processOpts", "pluginOpts", "vendor", "prefix", "prop", "match", "unprefixed", "replace", "post<PERSON>s<PERSON><PERSON><PERSON>", "getPostCssPlugins", "postcssWithPlugins", "curry", "ctx", "config", "isRspack", "Boolean", "env", "NEXT_RSPACK", "prependData", "sassPrependData", "additionalData", "sassAdditionalData", "implementation", "sassImplementation", "sassOptions", "lazyPostCSSInitializer", "experimental", "sassPreprocessors", "loader", "resolve", "options", "sourceMap", "fibers", "silenceDeprecations", "fns", "google<PERSON><PERSON>der", "localLoader", "nextFontLoaders", "for<PERSON>ach", "fontLoaderTarget", "fontLoaderPath", "push", "oneOf", "sideEffects", "test", "use", "getNextFontLoader", "issuer", "reason", "getCustomDocumentError", "shouldIncludeExternalCSSImports", "craCompat", "transpilePackages", "hasAppDir", "issuer<PERSON><PERSON>er", "cssModules", "getCssModuleLoader", "isAppDir", "filter", "nonNullable", "getLocalModuleImportError", "isServer", "isProduction", "allowedPagesGlobalCSSPath", "undefined", "and", "allowedPagesGlobalCSSIssuer", "getGlobalCssLoader", "include", "customAppFile", "getGlobalModuleImportError", "getGlobalImportError", "isClient", "exclude", "type", "MiniCssExtractPlugin", "getRspackCore", "CssExtractRspackPlugin", "default", "filename", "chunkFilename", "ignoreOrder", "insert", "linkTag", "_N_E_STYLE_LOAD", "href", "onload", "onerror", "indexOf", "window", "location", "origin", "URL", "pathname", "then", "call", "document", "head", "append<PERSON><PERSON><PERSON>", "fn", "pipe"], "mappings": ";;;;;;;;;;;;;;;;IAkJaA,GAAG;eAAHA;;IAzFSC,WAAW;eAAXA;;IAtCTC,YAAY;eAAZA;;;oEAnBK;yBAEa;uBACV;yBAEkC;0BACrB;0BAM3B;yBAC2B;6BACN;2BACG;2BACD;;;;;;AAGvB,MAAMA,eAAe;AAE5B,2BAA2B;AAC3B,MAAMC,iBAAiB;AACvB,MAAMC,kBAAkB;AAExB,iDAAiD;AACjD,MAAMC,kBAAkB;AACxB,MAAMC,mBAAmB;AAEzB,MAAMC,iBAAiB;IACrBC,IAAI;QACFC,yBAAc,CAACC,qBAAqB;QACpCD,yBAAc,CAACE,mBAAmB;QAClCF,yBAAc,CAACG,eAAe;KAC/B;AACH;AAEA,MAAMC,mBAAmB;IACvBC,KAAK;QACHL,yBAAc,CAACC,qBAAqB;QACpCD,yBAAc,CAACE,mBAAmB;QAClCF,yBAAc,CAACG,eAAe;KAC/B;AACH;AAEA;;CAEC,GACD,SAASG,cAAcC,CAAsB;IAC3CC,OAAOC,cAAc,CAACF,GAAGG,OAAOC,GAAG,CAAC,sBAAsB;QACxDC,YAAY;QACZC,OAAO;IACT;IACA,OAAON;AACT;AAEA,IAAIO;AACG,eAAetB,YACpBuB,aAAqB,EACrBC,iBAAuC,EACvCC,uBAA4C,EAC5CC,eAAoC;IAEpC,IAAI,CAACJ,wBAAwB;QAC3BA,yBAAyB,AAAC,CAAA;YACxB,MAAMK,UAAUC,QAAQ;YACxB,8BAA8B;YAC9BD,QAAQE,MAAM,GAAG,SAASC,cAAcC,IAAI,EAAEC,WAAW;gBACvD,SAASC,QAAQ,GAAGC,IAAS;oBAC3B,IAAIC,cAAcH,eAAeE;oBACjCC,YAAYL,aAAa,GAAGC;oBAC5B,uDAAuD;oBACvD,OAAOI;gBACT;gBAEA,IAAIC;gBACJpB,OAAOC,cAAc,CAACgB,SAAS,WAAW;oBACxCI;wBACE,IAAI,CAACD,OAAOA,QAAQH;wBACpB,OAAOG;oBACT;gBACF;gBAEAH,QAAQK,OAAO,GAAG,SAChBvC,GAAQ,EACRwC,WAAgB,EAChBC,UAAe;oBAEf,OAAOb,QAAQ;wBAACM,QAAQO;qBAAY,EAAEF,OAAO,CAACvC,KAAKwC;gBACrD;gBAEA,OAAON;YACT;YAEA,8BAA8B;YAC9BN,QAAQc,MAAM,GAAG;gBACf;;;;;;SAMC,GACDC,QAAQ,SAASA,OAAOC,IAAY;oBAClC,MAAMC,QAAQD,KAAKC,KAAK,CAAC;oBAEzB,IAAIA,OAAO;wBACT,OAAOA,KAAK,CAAC,EAAE;oBACjB;oBAEA,OAAO;gBACT;gBAEA;;;;;SAKC,GACDC,YAAY,SAASA,WACnB;;WAEC,GACDF,IAAY;oBAEZ,OAAOA,KAAKG,OAAO,CAAC,UAAU;gBAChC;YACF;YAEA,MAAMC,iBAAiB,MAAMC,IAAAA,0BAAiB,EAC5CzB,eACAC,mBACAC,yBACAC;YAGF,OAAO;gBACLC;gBACAsB,oBAAoBtB,QAAQoB;YAC9B;QACF,CAAA;IACF;IAEA,OAAOzB;AACT;AAEO,MAAMvB,MAAMmD,IAAAA,oBAAK,EAAC,eAAenD,IACtCoD,GAAyB,EACzBC,MAA6B;IAE7B,MAAMC,WAAWC,QAAQhB,QAAQiB,GAAG,CAACC,WAAW;IAChD,MAAM,EACJC,aAAaC,eAAe,EAC5BC,gBAAgBC,kBAAkB,EAClCC,gBAAgBC,kBAAkB,EAClC,GAAGC,aACJ,GAAGZ,IAAIY,WAAW;IAEnB,MAAMC,yBAAyB,IAC7BhE,YACEmD,IAAI5B,aAAa,EACjB4B,IAAI3B,iBAAiB,EACrB2B,IAAIc,YAAY,CAACxC,uBAAuB,EACxC0B,IAAIc,YAAY,CAACvC,eAAe;IAGpC,MAAMwC,oBAA8C;QAClD,qEAAqE;QACrE,wCAAwC;QACxC;YACEC,QAAQvC,QAAQwC,OAAO,CAAC;YACxBC,SAAS;gBACPR,gBAAgBC;gBAChB,mEAAmE;gBACnE,4CAA4C;gBAC5CQ,WAAW;gBACXP,aAAa;oBACX,sEAAsE;oBACtE,sEAAsE;oBACtE,wBAAwB;oBACxB,8GAA8G;oBAC9G,iDAAiD;oBACjD,oEAAoE;oBACpE,0BAA0B;oBAC1BQ,QAAQ;oBACR,sDAAsD;oBACtDC,qBAAqB;wBAAC;qBAAgB;oBACtC,GAAGT,WAAW;gBAChB;gBACAJ,gBAAgBD,mBAAmBE;YACrC;QACF;QACA,yEAAyE;QACzE,uEAAuE;QACvE,WAAW;QACX,8DAA8D;QAC9D,8BAA8B;QAC9B;YACEO,QAAQvC,QAAQwC,OAAO,CAAC;YACxBC,SAAS;gBACP1C,SAASqC;gBACT,6DAA6D;gBAC7D,QAAQ;gBACRM,WAAW;YACb;QACF;KACD;IAED,MAAMG,MAAyB,EAAE;IAEjC,MAAMC,eAAe9C,QAAQwC,OAAO,CAClC;IAEF,MAAMO,cAAc/C,QAAQwC,OAAO,CACjC;IAEF,MAAMQ,kBAA0D;QAC9D;YAAChD,QAAQwC,OAAO,CAAC;YAAgCM;SAAa;QAC9D;YAAC9C,QAAQwC,OAAO,CAAC;YAA+BO;SAAY;KAC7D;IAEDC,gBAAgBC,OAAO,CAAC,CAAC,CAACC,kBAAkBC,eAAe;QACzD,uEAAuE;QACvEN,IAAIO,IAAI,CACNb,IAAAA,eAAM,EAAC;YACLc,OAAO;gBACLnE,cAAc;oBACZoE,aAAa;oBACbC,MAAML;oBACNM,KAAKC,IAAAA,2BAAiB,EAAClC,KAAKa,wBAAwBe;gBACtD;aACD;QACH;IAEJ;IAEA,4EAA4E;IAC5E,gDAAgD;IAChDN,IAAIO,IAAI,CACNb,IAAAA,eAAM,EAAC;QACLc,OAAO;YACLnE,cAAc;gBACZqE,MAAMlF;gBACN,iEAAiE;gBACjE,wCAAwC;gBACxCqF,QAAQ;gBACRF,KAAK;oBACHjB,QAAQ;oBACRE,SAAS;wBACPkB,QAAQC,IAAAA,gCAAsB;oBAChC;gBACF;YACF;SACD;IACH;IAGF,MAAMC,kCACJ,CAAC,CAACtC,IAAIc,YAAY,CAACyB,SAAS,IAAI,CAAC,CAACvC,IAAIwC,iBAAiB;IAEzD,mFAAmF;IACnFlB,IAAIO,IAAI,CACN,gEAAgE;IAChE,4DAA4D;IAC5D,iEAAiE;IACjE,uCAAuC;IACvCb,IAAAA,eAAM,EAAC;QACLc,OAAO;YACL,wDAAwD;YACxD9B,IAAIyC,SAAS,GACT9E,cAAc;gBACZoE,aAAa;gBACbC,MAAMhF;gBACN0F,aAAavF;gBACb8E,KAAK;oBACH;wBACEjB,QAAQvC,QAAQwC,OAAO,CACrB;wBAEFC,SAAS;4BACPyB,YAAY;wBACd;oBACF;uBACGC,IAAAA,2BAAkB,EACnB;wBAAE,GAAG5C,GAAG;wBAAE6C,UAAU;oBAAK,GACzBhC;iBAEH;YACH,KACA;YACJlD,cAAc;gBACZoE,aAAa;gBACbC,MAAMhF;gBACN0F,aAAajF;gBACbwE,KAAKW,IAAAA,2BAAkB,EACrB;oBAAE,GAAG5C,GAAG;oBAAE6C,UAAU;gBAAM,GAC1BhC;YAEJ;SACD,CAACiC,MAAM,CAACC,wBAAW;IACtB,IACA,6DAA6D;IAC7D,iEAAiE;IACjE,6DAA6D;IAC7D,kEAAkE;IAClE,uCAAuC;IACvC/B,IAAAA,eAAM,EAAC;QACLc,OAAO;YACL,wDAAwD;YACxD9B,IAAIyC,SAAS,GACT9E,cAAc;gBACZoE,aAAa;gBACbC,MAAM9E;gBACNwF,aAAavF;gBACb8E,KAAK;oBACH;wBACEjB,QAAQvC,QAAQwC,OAAO,CACrB;wBAEFC,SAAS;4BACPyB,YAAY;wBACd;oBACF;uBACGC,IAAAA,2BAAkB,EACnB;wBAAE,GAAG5C,GAAG;wBAAE6C,UAAU;oBAAK,GACzBhC,wBACAE;iBAEH;YACH,KACA;YACJpD,cAAc;gBACZoE,aAAa;gBACbC,MAAM9E;gBACNwF,aAAajF;gBACbwE,KAAKW,IAAAA,2BAAkB,EACrB;oBAAE,GAAG5C,GAAG;oBAAE6C,UAAU;gBAAM,GAC1BhC,wBACAE;YAEJ;SACD,CAAC+B,MAAM,CAACC,wBAAW;IACtB,IACA,oEAAoE;IACpE/B,IAAAA,eAAM,EAAC;QACLc,OAAO;YACLnE,cAAc;gBACZqE,MAAM;oBAAChF;oBAAiBE;iBAAiB;gBACzC+E,KAAK;oBACHjB,QAAQ;oBACRE,SAAS;wBACPkB,QAAQY,IAAAA,mCAAyB;oBACnC;gBACF;YACF;SACD;IACH;IAGF,+BAA+B;IAC/B,IAAIhD,IAAIiD,QAAQ,EAAE;QAChB3B,IAAIO,IAAI,CACNb,IAAAA,eAAM,EAAC;YACLc,OAAO;gBACL9B,IAAIyC,SAAS,IAAI,CAACzC,IAAIkD,YAAY,GAC9BvF,cAAc;oBACZoE,aAAa;oBACbC,MAAM;wBAACjF;wBAAgBE;qBAAgB;oBACvCyF,aAAavF;oBACb8E,KAAK;wBACHjB,QAAQvC,QAAQwC,OAAO,CACrB;wBAEFC,SAAS;4BACPyB,YAAY;wBACd;oBACF;gBACF,KACA;gBACJhF,cAAc;oBACZ,0DAA0D;oBAC1DoE,aAAa;oBACbC,MAAM;wBAACjF;wBAAgBE;qBAAgB;oBACvCgF,KAAKxD,QAAQwC,OAAO,CAAC;gBACvB;aACD,CAAC6B,MAAM,CAACC,wBAAW;QACtB;IAEJ,OAAO;QACL,iFAAiF;QACjF,yCAAyC;QACzC,iDAAiD;QACjD,iGAAiG;QACjG,gDAAgD;QAChD,MAAMI,4BAA4BnD,IAAIyC,SAAS,GAC3CW,YACA;YACEC,KAAK;gBACH;oBACEjG,IAAI;wBACF;wBACA;4BACEM,KAAK;gCAACsC,IAAI5B,aAAa;6BAAC;wBAC1B;qBACD;gBACH;aACD;QACH;QACJ,MAAMkF,8BAA8BtD,IAAIyC,SAAS,GAC7CW,YACAd,kCACEc,YACA;YACEC,KAAK;gBAACrD,IAAI5B,aAAa;aAAC;YACxBV,KAAK;gBAAC;aAAe;QACvB;QAEN4D,IAAIO,IAAI,CACNb,IAAAA,eAAM,EAAC;YACLc,OAAO;mBACD9B,IAAIyC,SAAS,GACb;oBACE9E,cAAc;wBACZoE,aAAa;wBACbC,MAAMjF;wBACN2F,aAAavF;wBACb8E,KAAK;4BACH;gCACEjB,QAAQvC,QAAQwC,OAAO,CACrB;gCAEFC,SAAS;oCACPyB,YAAY;gCACd;4BACF;+BACGY,IAAAA,2BAAkB,EACnB;gCAAE,GAAGvD,GAAG;gCAAE6C,UAAU;4BAAK,GACzBhC;yBAEH;oBACH;oBACAlD,cAAc;wBACZoE,aAAa;wBACbC,MAAM/E;wBACNyF,aAAavF;wBACb8E,KAAK;4BACH;gCACEjB,QAAQvC,QAAQwC,OAAO,CACrB;gCAEFC,SAAS;oCACPyB,YAAY;gCACd;4BACF;+BACGY,IAAAA,2BAAkB,EACnB;gCAAE,GAAGvD,GAAG;gCAAE6C,UAAU;4BAAK,GACzBhC,wBACAE;yBAEH;oBACH;iBACD,GACD,EAAE;gBACNpD,cAAc;oBACZoE,aAAa;oBACbC,MAAMjF;oBACNyG,SAASL;oBACThB,QAAQmB;oBACRZ,aAAajF;oBACbwE,KAAKsB,IAAAA,2BAAkB,EACrB;wBAAE,GAAGvD,GAAG;wBAAE6C,UAAU;oBAAM,GAC1BhC;gBAEJ;gBACAlD,cAAc;oBACZoE,aAAa;oBACbC,MAAM/E;oBACNuG,SAASL;oBACThB,QAAQmB;oBACRZ,aAAajF;oBACbwE,KAAKsB,IAAAA,2BAAkB,EACrB;wBAAE,GAAGvD,GAAG;wBAAE6C,UAAU;oBAAM,GAC1BhC,wBACAE;gBAEJ;aACD,CAAC+B,MAAM,CAACC,wBAAW;QACtB;QAGF,IAAI/C,IAAIyD,aAAa,EAAE;YACrBnC,IAAIO,IAAI,CACNb,IAAAA,eAAM,EAAC;gBACLc,OAAO;oBACLnE,cAAc;wBACZoE,aAAa;wBACbC,MAAMjF;wBACNoF,QAAQ;4BAAEkB,KAAK;gCAACrD,IAAIyD,aAAa;6BAAC;wBAAC;wBACnCxB,KAAKsB,IAAAA,2BAAkB,EACrB;4BAAE,GAAGvD,GAAG;4BAAE6C,UAAU;wBAAM,GAC1BhC;oBAEJ;iBACD;YACH,IACAG,IAAAA,eAAM,EAAC;gBACLc,OAAO;oBACLnE,cAAc;wBACZoE,aAAa;wBACbC,MAAM/E;wBACNkF,QAAQ;4BAAEkB,KAAK;gCAACrD,IAAIyD,aAAa;6BAAC;wBAAC;wBACnCxB,KAAKsB,IAAAA,2BAAkB,EACrB;4BAAE,GAAGvD,GAAG;4BAAE6C,UAAU;wBAAM,GAC1BhC,wBACAE;oBAEJ;iBACD;YACH;QAEJ;IACF;IAEA,8DAA8D;IAC9D,IAAI,CAACuB,iCAAiC;QACpChB,IAAIO,IAAI,CACNb,IAAAA,eAAM,EAAC;YACLc,OAAO;gBACLnE,cAAc;oBACZqE,MAAM;wBAACjF;wBAAgBE;qBAAgB;oBACvCkF,QAAQ;wBAAEkB,KAAK;4BAAC;yBAAe;oBAAC;oBAChCpB,KAAK;wBACHjB,QAAQ;wBACRE,SAAS;4BACPkB,QAAQsB,IAAAA,oCAA0B;wBACpC;oBACF;gBACF;aACD;QACH;IAEJ;IAEA,sEAAsE;IACtEpC,IAAIO,IAAI,CACNb,IAAAA,eAAM,EAAC;QACLc,OAAO;YACLnE,cAAc;gBACZqE,MAAM;oBAACjF;oBAAgBE;iBAAgB;gBACvCkF,QAAQnC,IAAIyC,SAAS,GACjB;oBACE,oEAAoE;oBACpE,kBAAkB;oBAClBY,KAAK;wBAACrD,IAAI5B,aAAa;qBAAC;oBACxBV,KAAK;wBAAC;qBAA+B;gBACvC,IACA0F;gBACJnB,KAAK;oBACHjB,QAAQ;oBACRE,SAAS;wBACPkB,QAAQuB,IAAAA,8BAAoB;oBAC9B;gBACF;YACF;SACD;IACH;IAGF,IAAI3D,IAAI4D,QAAQ,EAAE;QAChB,qEAAqE;QACrE,uBAAuB;QACvBtC,IAAIO,IAAI,CACNb,IAAAA,eAAM,EAAC;YACLc,OAAO;gBACLnE,cAAc;oBACZ,2CAA2C;oBAC3CwE,QAAQrF;oBACR,qDAAqD;oBACrD+G,SAAS;wBACP;wBACA;wBACA;wBACA;qBACD;oBACD,+DAA+D;oBAC/D,uCAAuC;oBACvCC,MAAM;gBACR;aACD;QACH;IAEJ;IAEA,yEAAyE;IACzE,IAAI9D,IAAI4D,QAAQ,IAAK5D,CAAAA,IAAIkD,YAAY,IAAIlD,IAAIyC,SAAS,AAAD,GAAI;QACvD,mEAAmE;QACnE,MAAMsB,uBAAuB7D,WACzB8D,IAAAA,wBAAa,IAAGC,sBAAsB,GACtCxF,QAAQ,4CAA4CyF,OAAO;QAE/D5C,IAAIO,IAAI,CACNnD,IAAAA,eAAM,EACJ,8BAA8B;QAC9B,IAAIqF,qBAAqB;YACvBI,UAAUnE,IAAIkD,YAAY,GACtB,iCACA;YACJkB,eAAepE,IAAIkD,YAAY,GAC3B,iCACA;YACJ,qEAAqE;YACrE,gBAAgB;YAChB,kEAAkE;YAClE,mEAAmE;YACnE,8CAA8C;YAC9C,EAAE;YACF,iEAAiE;YACjE,4DAA4D;YAC5D,EAAE;YACF,qEAAqE;YACrE,4CAA4C;YAC5CmB,aAAa;YACbC,QAAQ,SAAUC,OAAwB;gBACxC,IAAI,OAAOC,oBAAoB,YAAY;oBACzC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE,GAAGJ;oBAClCC,gBACEC,KAAKG,OAAO,CAACC,OAAOC,QAAQ,CAACC,MAAM,MAAM,IACrC,IAAIC,IAAIP,MAAMQ,QAAQ,GACtBR,MACJS,IAAI,CACJ,IAAMR,0BAAAA,OAAQS,IAAI,CAACZ,SAAS;4BAAET,MAAM;wBAAO,IAC3C,IAAMa,2BAAAA,QAASQ,IAAI,CAACZ,SAAS,CAAC;gBAElC,OAAO;oBACLa,SAASC,IAAI,CAACC,WAAW,CAACf;gBAC5B;YACF;QACF;IAGN;IAEA,MAAMgB,KAAKC,IAAAA,WAAI,KAAIlE;IACnB,OAAOiE,GAAGtF;AACZ"}