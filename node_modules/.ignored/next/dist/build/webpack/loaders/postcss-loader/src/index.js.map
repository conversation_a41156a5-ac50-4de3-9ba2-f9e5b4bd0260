{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/postcss-loader/src/index.ts"], "sourcesContent": ["import Warning from './Warning'\nimport SyntaxError from './Error'\nimport { normalizeSourceMap, normalizeSourceMapAfterPostcss } from './utils'\n\n/**\n * **PostCSS Loader**\n *\n * Loads && processes CSS with [PostCSS](https://github.com/postcss/postcss)\n */\nexport default async function loader(\n  this: any,\n  /** Source */\n  content: string,\n  /** Source Map */\n  sourceMap: any,\n  meta: any\n): Promise<void> {\n  const loaderSpan = this.currentTraceSpan.traceChild('postcss-loader')\n  const callback = this.async()\n\n  loaderSpan\n    .traceAsyncFn(async () => {\n      const options = this.getOptions()\n      const file = this.resourcePath\n\n      const useSourceMap =\n        typeof options.sourceMap !== 'undefined'\n          ? options.sourceMap\n          : this.sourceMap\n\n      const processOptions: any = {\n        from: file,\n        to: file,\n      }\n\n      if (useSourceMap) {\n        processOptions.map = {\n          inline: false,\n          annotation: false,\n          ...processOptions.map,\n        }\n      }\n\n      if (sourceMap && processOptions.map) {\n        processOptions.map.prev = loaderSpan\n          .traceChild('normalize-source-map')\n          .traceFn(() => normalizeSourceMap(sourceMap, this.context))\n      }\n\n      let root: any\n\n      // Reuse PostCSS AST from other loaders\n      if (meta && meta.ast && meta.ast.type === 'postcss') {\n        ;({ root } = meta.ast)\n        loaderSpan.setAttribute('astUsed', 'true')\n      }\n\n      // Initializes postcss with plugins\n      const { postcssWithPlugins } = await options.postcss()\n\n      let result\n\n      try {\n        result = await loaderSpan\n          .traceChild('postcss-process')\n          .traceAsyncFn(() =>\n            postcssWithPlugins.process(root || content, processOptions)\n          )\n      } catch (error: any) {\n        if (error.file) {\n          this.addDependency(error.file)\n        }\n\n        if (error.name === 'CssSyntaxError') {\n          throw new SyntaxError(error)\n        }\n\n        throw error\n      }\n\n      for (const warning of result.warnings()) {\n        this.emitWarning(new Warning(warning))\n      }\n\n      for (const message of result.messages) {\n        // eslint-disable-next-line default-case\n        switch (message.type) {\n          case 'dependency':\n            this.addDependency(message.file)\n            break\n          case 'build-dependency':\n            this.addBuildDependency(message.file)\n            break\n          case 'missing-dependency':\n            this.addMissingDependency(message.file)\n            break\n          case 'context-dependency':\n            this.addContextDependency(message.file)\n            break\n          case 'dir-dependency':\n            this.addContextDependency(message.dir)\n            break\n          case 'asset':\n            if (message.content && message.file) {\n              this.emitFile(\n                message.file,\n                message.content,\n                message.sourceMap,\n                message.info\n              )\n            }\n        }\n      }\n\n      // eslint-disable-next-line no-undefined\n      let map = result.map ? result.map.toJSON() : undefined\n\n      if (map && useSourceMap) {\n        map = normalizeSourceMapAfterPostcss(map, this.context)\n      }\n\n      const ast = {\n        type: 'postcss',\n        version: result.processor.version,\n        root: result.root,\n      }\n\n      return [result.css, map, { ast }]\n    })\n    .then(\n      ([css, map, { ast }]: any) => {\n        callback?.(null, css, map, { ast })\n      },\n      (err: Error) => {\n        callback?.(err)\n      }\n    )\n}\n"], "names": ["loader", "content", "sourceMap", "meta", "loaderSpan", "currentTraceSpan", "<PERSON><PERSON><PERSON><PERSON>", "callback", "async", "traceAsyncFn", "options", "getOptions", "file", "resourcePath", "useSourceMap", "processOptions", "from", "to", "map", "inline", "annotation", "prev", "traceFn", "normalizeSourceMap", "context", "root", "ast", "type", "setAttribute", "postcssWithPlugins", "postcss", "result", "process", "error", "addDependency", "name", "SyntaxError", "warning", "warnings", "emitWarning", "Warning", "message", "messages", "addBuildDependency", "addMissingDependency", "addContextDependency", "dir", "emitFile", "info", "toJSON", "undefined", "normalizeSourceMapAfterPostcss", "version", "processor", "css", "then", "err"], "mappings": ";;;;+BAIA;;;;CAIC,GACD;;;eAA8BA;;;gEATV;8DACI;uBAC2C;;;;;;AAOpD,eAAeA,OAE5B,WAAW,GACXC,OAAe,EACf,eAAe,GACfC,SAAc,EACdC,IAAS;IAET,MAAMC,aAAa,IAAI,CAACC,gBAAgB,CAACC,UAAU,CAAC;IACpD,MAAMC,WAAW,IAAI,CAACC,KAAK;IAE3BJ,WACGK,YAAY,CAAC;QACZ,MAAMC,UAAU,IAAI,CAACC,UAAU;QAC/B,MAAMC,OAAO,IAAI,CAACC,YAAY;QAE9B,MAAMC,eACJ,OAAOJ,QAAQR,SAAS,KAAK,cACzBQ,QAAQR,SAAS,GACjB,IAAI,CAACA,SAAS;QAEpB,MAAMa,iBAAsB;YAC1BC,MAAMJ;YACNK,IAAIL;QACN;QAEA,IAAIE,cAAc;YAChBC,eAAeG,GAAG,GAAG;gBACnBC,QAAQ;gBACRC,YAAY;gBACZ,GAAGL,eAAeG,GAAG;YACvB;QACF;QAEA,IAAIhB,aAAaa,eAAeG,GAAG,EAAE;YACnCH,eAAeG,GAAG,CAACG,IAAI,GAAGjB,WACvBE,UAAU,CAAC,wBACXgB,OAAO,CAAC,IAAMC,IAAAA,yBAAkB,EAACrB,WAAW,IAAI,CAACsB,OAAO;QAC7D;QAEA,IAAIC;QAEJ,uCAAuC;QACvC,IAAItB,QAAQA,KAAKuB,GAAG,IAAIvB,KAAKuB,GAAG,CAACC,IAAI,KAAK,WAAW;;YACjD,CAAA,EAAEF,IAAI,EAAE,GAAGtB,KAAKuB,GAAG,AAAD;YACpBtB,WAAWwB,YAAY,CAAC,WAAW;QACrC;QAEA,mCAAmC;QACnC,MAAM,EAAEC,kBAAkB,EAAE,GAAG,MAAMnB,QAAQoB,OAAO;QAEpD,IAAIC;QAEJ,IAAI;YACFA,SAAS,MAAM3B,WACZE,UAAU,CAAC,mBACXG,YAAY,CAAC,IACZoB,mBAAmBG,OAAO,CAACP,QAAQxB,SAASc;QAElD,EAAE,OAAOkB,OAAY;YACnB,IAAIA,MAAMrB,IAAI,EAAE;gBACd,IAAI,CAACsB,aAAa,CAACD,MAAMrB,IAAI;YAC/B;YAEA,IAAIqB,MAAME,IAAI,KAAK,kBAAkB;gBACnC,MAAM,qBAAsB,CAAtB,IAAIC,cAAW,CAACH,QAAhB,qBAAA;2BAAA;gCAAA;kCAAA;gBAAqB;YAC7B;YAEA,MAAMA;QACR;QAEA,KAAK,MAAMI,WAAWN,OAAOO,QAAQ,GAAI;YACvC,IAAI,CAACC,WAAW,CAAC,qBAAoB,CAApB,IAAIC,gBAAO,CAACH,UAAZ,qBAAA;uBAAA;4BAAA;8BAAA;YAAmB;QACtC;QAEA,KAAK,MAAMI,WAAWV,OAAOW,QAAQ,CAAE;YACrC,wCAAwC;YACxC,OAAQD,QAAQd,IAAI;gBAClB,KAAK;oBACH,IAAI,CAACO,aAAa,CAACO,QAAQ7B,IAAI;oBAC/B;gBACF,KAAK;oBACH,IAAI,CAAC+B,kBAAkB,CAACF,QAAQ7B,IAAI;oBACpC;gBACF,KAAK;oBACH,IAAI,CAACgC,oBAAoB,CAACH,QAAQ7B,IAAI;oBACtC;gBACF,KAAK;oBACH,IAAI,CAACiC,oBAAoB,CAACJ,QAAQ7B,IAAI;oBACtC;gBACF,KAAK;oBACH,IAAI,CAACiC,oBAAoB,CAACJ,QAAQK,GAAG;oBACrC;gBACF,KAAK;oBACH,IAAIL,QAAQxC,OAAO,IAAIwC,QAAQ7B,IAAI,EAAE;wBACnC,IAAI,CAACmC,QAAQ,CACXN,QAAQ7B,IAAI,EACZ6B,QAAQxC,OAAO,EACfwC,QAAQvC,SAAS,EACjBuC,QAAQO,IAAI;oBAEhB;YACJ;QACF;QAEA,wCAAwC;QACxC,IAAI9B,MAAMa,OAAOb,GAAG,GAAGa,OAAOb,GAAG,CAAC+B,MAAM,KAAKC;QAE7C,IAAIhC,OAAOJ,cAAc;YACvBI,MAAMiC,IAAAA,qCAA8B,EAACjC,KAAK,IAAI,CAACM,OAAO;QACxD;QAEA,MAAME,MAAM;YACVC,MAAM;YACNyB,SAASrB,OAAOsB,SAAS,CAACD,OAAO;YACjC3B,MAAMM,OAAON,IAAI;QACnB;QAEA,OAAO;YAACM,OAAOuB,GAAG;YAAEpC;YAAK;gBAAEQ;YAAI;SAAE;IACnC,GACC6B,IAAI,CACH,CAAC,CAACD,KAAKpC,KAAK,EAAEQ,GAAG,EAAE,CAAM;QACvBnB,4BAAAA,SAAW,MAAM+C,KAAKpC,KAAK;YAAEQ;QAAI;IACnC,GACA,CAAC8B;QACCjD,4BAAAA,SAAWiD;IACb;AAEN"}