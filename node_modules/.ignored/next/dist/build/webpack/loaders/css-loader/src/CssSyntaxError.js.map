{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/css-loader/src/CssSyntaxError.ts"], "sourcesContent": ["export default class CssSyntaxError extends Error {\n  stack: any\n  constructor(error: any) {\n    super(error)\n\n    const { reason, line, column } = error\n\n    this.name = 'CssSyntaxError'\n\n    // Based on https://github.com/postcss/postcss/blob/master/lib/css-syntax-error.es6#L132\n    // We don't need `plugin` and `file` properties.\n    this.message = `${this.name}\\n\\n`\n\n    if (typeof line !== 'undefined') {\n      this.message += `(${line}:${column}) `\n    }\n\n    this.message += reason\n\n    const code = error.showSourceCode()\n\n    if (code) {\n      this.message += `\\n\\n${code}\\n`\n    }\n\n    // We don't need stack https://github.com/postcss/postcss/blob/master/docs/guidelines/runner.md#31-dont-show-js-stack-for-csssyntaxerror\n    this.stack = false\n  }\n}\n"], "names": ["CssSyntaxError", "Error", "constructor", "error", "reason", "line", "column", "name", "message", "code", "showSourceCode", "stack"], "mappings": ";;;;+BAAA;;;eAAqBA;;;AAAN,MAAMA,uBAAuBC;IAE1CC,YAAYC,KAAU,CAAE;QACtB,KAAK,CAACA;QAEN,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAE,GAAGH;QAEjC,IAAI,CAACI,IAAI,GAAG;QAEZ,wFAAwF;QACxF,gDAAgD;QAChD,IAAI,CAACC,OAAO,GAAG,GAAG,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;QAEjC,IAAI,OAAOF,SAAS,aAAa;YAC/B,IAAI,CAACG,OAAO,IAAI,CAAC,CAAC,EAAEH,KAAK,CAAC,EAAEC,OAAO,EAAE,CAAC;QACxC;QAEA,IAAI,CAACE,OAAO,IAAIJ;QAEhB,MAAMK,OAAON,MAAMO,cAAc;QAEjC,IAAID,MAAM;YACR,IAAI,CAACD,OAAO,IAAI,CAAC,IAAI,EAAEC,KAAK,EAAE,CAAC;QACjC;QAEA,wIAAwI;QACxI,IAAI,CAACE,KAAK,GAAG;IACf;AACF"}