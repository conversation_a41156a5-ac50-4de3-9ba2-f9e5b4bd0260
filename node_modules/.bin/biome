#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Work/learnfunda/code/node_modules/.pnpm/@biomejs+biome@2.0.6/node_modules/@biomejs/biome/bin/node_modules:/Users/<USER>/Work/learnfunda/code/node_modules/.pnpm/@biomejs+biome@2.0.6/node_modules/@biomejs/biome/node_modules:/Users/<USER>/Work/learnfunda/code/node_modules/.pnpm/@biomejs+biome@2.0.6/node_modules/@biomejs/node_modules:/Users/<USER>/Work/learnfunda/code/node_modules/.pnpm/@biomejs+biome@2.0.6/node_modules:/Users/<USER>/Work/learnfunda/code/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Work/learnfunda/code/node_modules/.pnpm/@biomejs+biome@2.0.6/node_modules/@biomejs/biome/bin/node_modules:/Users/<USER>/Work/learnfunda/code/node_modules/.pnpm/@biomejs+biome@2.0.6/node_modules/@biomejs/biome/node_modules:/Users/<USER>/Work/learnfunda/code/node_modules/.pnpm/@biomejs+biome@2.0.6/node_modules/@biomejs/node_modules:/Users/<USER>/Work/learnfunda/code/node_modules/.pnpm/@biomejs+biome@2.0.6/node_modules:/Users/<USER>/Work/learnfunda/code/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/@biomejs+biome@2.0.6/node_modules/@biomejs/biome/bin/biome" "$@"
else
  exec node  "$basedir/../.pnpm/@biomejs+biome@2.0.6/node_modules/@biomejs/biome/bin/biome" "$@"
fi
